package v1

import (
	"admin-api/api/common"
	"admin-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// --- 获取我的结算记录列表 ---

// GetMySettlementsReq defines the request structure for getting merchant's own settlement records.
type GetMySettlementsReq struct {
	g.Meta `path:"/my/settlements" method:"get" tags:"MerchantSettlements" summary:"获取我的结算记录"`
	common.PageRequest
	TokenId   *uint    `json:"tokenId" dc:"币种ID (筛选特定币种)"`
	TokenName string   `json:"tokenName" dc:"币种名称 (模糊搜索)"`
	State     *uint    `json:"state" dc:"状态 (1-待审核, 2-处理中, 3-已拒绝, 4-已完成, 5-失败, 6-已撤销)"`
	OrderNo   string   `json:"orderNo" dc:"结算订单号 (模糊搜索)"`
	TxHash    string   `json:"txHash" dc:"交易哈希 (模糊搜索)"`
	Address   string   `json:"address" dc:"结算地址 (模糊搜索)"`
	AmountMin *string  `json:"amountMin" dc:"最小金额"`
	AmountMax *string  `json:"amountMax" dc:"最大金额"`
	DateRange string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export    bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantSettlementInfoType defines the structure for settlement information in a list.
type MerchantSettlementInfoType struct {
	WithdrawsId        uint            `json:"withdrawsId" dc:"结算记录ID"`
	TokenId            uint            `json:"tokenId" dc:"币种ID"`
	TokenName          string          `json:"tokenName" dc:"币种名称"`
	Chan               string          `json:"chan" dc:"结算渠道"`
	OrderNo            string          `json:"orderNo" dc:"结算订单号"`
	Address            string          `json:"address" dc:"结算目标地址"`
	RecipientName      string          `json:"recipientName" dc:"法币收款人姓名"`
	RecipientAccount   string          `json:"recipientAccount" dc:"法币收款账户"`
	Amount             decimal.Decimal `json:"amount" dc:"申请结算金额"`
	HandlingFee        decimal.Decimal `json:"handlingFee" dc:"结算手续费"`
	ActualAmount       decimal.Decimal `json:"actualAmount" dc:"实际到账金额"`
	State              uint            `json:"state" dc:"状态值"`
	StateText          string          `json:"stateText" dc:"状态描述"`
	RefuseReasonZh     string          `json:"refuseReasonZh" dc:"拒绝原因(中文)"`
	RefuseReasonEn     string          `json:"refuseReasonEn" dc:"拒绝原因(英文)"`
	TxHash             string          `json:"txHash" dc:"交易哈希"`
	UserRemark         string          `json:"userRemark" dc:"用户备注"`
	AdminRemark        string          `json:"adminRemark" dc:"管理员备注"`
	FiatType           string          `json:"fiatType" dc:"法币结算类型"`
	CreatedAt          *gtime.Time     `json:"createdAt" dc:"创建时间"`
	CheckedAt          *gtime.Time     `json:"checkedAt" dc:"审核时间"`
	ProcessingAt       *gtime.Time     `json:"processingAt" dc:"处理开始时间"`
	CompletedAt        *gtime.Time     `json:"completedAt" dc:"完成时间"`
	NotificationSent   uint            `json:"notificationSent" dc:"通知状态"`
	NotificationSentAt *gtime.Time     `json:"notificationSentAt" dc:"通知发送时间"`
	CanCancel          bool            `json:"canCancel" dc:"是否可以撤销"`
}

// GetMySettlementsRes defines the response structure for getting merchant's own settlement records.
type GetMySettlementsRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of settlement records.
	Data []*MerchantSettlementInfoType `json:"data" dc:"结算记录列表数据"`
}

// --- 获取我的结算记录详情 ---

// GetMySettlementDetailReq defines the request structure for getting specific settlement record details.
type GetMySettlementDetailReq struct {
	g.Meta      `path:"/my/settlements/{withdrawsId}" method:"get" tags:"MerchantSettlements" summary:"获取我的结算记录详情"`
	WithdrawsId uint `json:"withdrawsId" v:"required#结算记录ID不能为空" dc:"结算记录ID"`
}

// MerchantSettlementDetailType defines the structure for detailed settlement information.
type MerchantSettlementDetailType struct {
	entity.MerchantSettlements
	StateText string `json:"stateText" dc:"状态描述"`
	CanCancel bool   `json:"canCancel" dc:"是否可以撤销"`
}

// GetMySettlementDetailRes defines the response structure for getting settlement record details.
type GetMySettlementDetailRes struct {
	// Data contains the settlement record details.
	Data *MerchantSettlementDetailType `json:"data" dc:"结算记录详情数据"`
}

// --- 撤销我的结算申请 ---

// CancelMySettlementReq defines the request structure for canceling a pending settlement.
type CancelMySettlementReq struct {
	g.Meta      `path:"/my/settlements/{withdrawsId}/cancel" method:"put" tags:"MerchantSettlements" summary:"撤销我的结算申请"`
	WithdrawsId uint   `json:"withdrawsId" v:"required#结算记录ID不能为空" dc:"结算记录ID"`
	Reason      string `json:"reason" v:"length:0,200" dc:"撤销原因"`
}

// CancelMySettlementRes defines the response structure after canceling a settlement.
type CancelMySettlementRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
	// Message provides additional information about the cancellation.
	Message string `json:"message" dc:"操作结果信息"`
}

// --- 创建我的结算申请 ---

// CreateMySettlementReq defines the request structure for creating a new settlement.
type CreateMySettlementReq struct {
	g.Meta     `path:"/my/settlements" method:"post" tags:"MerchantSettlements" summary:"创建我的结算申请"`
	TokenName  string `json:"tokenName" v:"required|length:1,50" dc:"币种名称"`
	Chain      string `json:"chain" v:"required|length:1,50" dc:"区块链网络"`
	OrderNo    string `json:"orderNo" v:"required|length:1,100" dc:"商户订单号，唯一标识"`
	Address    string `json:"address" v:"required|length:10,100" dc:"结算目标地址"`
	Amount     string `json:"amount" v:"required|regex:^[0-9]+(\\.[0-9]+)?$" dc:"结算金额"`
	UserRemark string `json:"userRemark" v:"length:0,200" dc:"用户备注"`
	Code       string `json:"code" v:"required|length:6,6" dc:"2FA验证码"`
}

// CreateMySettlementRes defines the response structure for creating a settlement.
type CreateMySettlementRes struct {
	// Data contains the created settlement information.
	Data *CreateMySettlementData `json:"data" dc:"创建的结算信息"`
}

// CreateMySettlementData defines the data structure returned after creating a settlement.
type CreateMySettlementData struct {
	WithdrawsId   uint   `json:"withdrawsId" dc:"结算记录ID"`
	OrderNo       string `json:"orderNo" dc:"商户订单号"`
	TokenName     string `json:"tokenName" dc:"币种名称"`
	Chain         string `json:"chain" dc:"区块链网络"`
	Address       string `json:"address" dc:"结算目标地址"`
	Amount        string `json:"amount" dc:"结算金额"`
	HandlingFee   string `json:"handlingFee" dc:"手续费"`
	ActualAmount  string `json:"actualAmount" dc:"实际到账金额"`
	Status        string `json:"status" dc:"状态"`
	EstimatedTime string `json:"estimatedTime" dc:"预计到账时间"`
}

// --- 获取我的结算手续费 ---

// GetMySettlementFeeReq defines the request structure for getting settlement fee.
type GetMySettlementFeeReq struct {
	g.Meta    `path:"/my/settlements/fee" method:"get" tags:"MerchantSettlements" summary:"获取我的结算手续费"`
	TokenName string `json:"tokenName" v:"required|length:1,50" dc:"币种名称"`
	Chain     string `json:"chain" v:"required|length:1,50" dc:"区块链网络"`
	Amount    string `json:"amount" v:"required|regex:^[0-9]+(\\.[0-9]+)?$" dc:"结算金额"`
}

// GetMySettlementFeeRes defines the response structure for getting settlement fee.
type GetMySettlementFeeRes struct {
	// Data contains the fee calculation information.
	Data *GetMySettlementFeeData `json:"data" dc:"手续费信息"`
}

// GetMySettlementFeeData defines the fee calculation data structure.
type GetMySettlementFeeData struct {
	TokenName     string `json:"tokenName" dc:"币种名称"`
	Chain         string `json:"chain" dc:"区块链网络"`
	Amount        string `json:"amount" dc:"结算金额"`
	HandlingFee   string `json:"handlingFee" dc:"手续费"`
	ActualAmount  string `json:"actualAmount" dc:"实际到账金额"`
	FeeRate       string `json:"feeRate" dc:"手续费率"`
	MinAmount     string `json:"minAmount" dc:"最小结算金额"`
	MaxAmount     string `json:"maxAmount" dc:"最大结算金额"`
	Available     string `json:"available" dc:"可用余额"`
	EstimatedTime string `json:"estimatedTime" dc:"预计到账时间"`
}

// --- 管理员审批结算申请 ---

// ApproveSettlementReq defines the request structure for approving a settlement.
type ApproveSettlementReq struct {
	g.Meta      `path:"/admin/settlements/{withdrawsId}/approve" method:"put" tags:"AdminSettlements" summary:"审批通过结算申请"`
	WithdrawsId uint   `json:"withdrawsId" v:"required#结算记录ID不能为空" dc:"结算记录ID"`
	Notes       string `json:"notes" v:"length:0,200" dc:"审批备注"`
}

// ApproveSettlementRes defines the response structure after approving a settlement.
type ApproveSettlementRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
	// Message provides additional information about the approval.
	Message string `json:"message" dc:"操作结果信息"`
	// TransactionIds contains the IDs of the generated audit transaction records.
	TransactionIds []uint64 `json:"transactionIds" dc:"生成的审计交易记录ID"`
}

// --- 管理员拒绝结算申请 ---

// RejectSettlementReq defines the request structure for rejecting a settlement.
type RejectSettlementReq struct {
	g.Meta      `path:"/admin/settlements/{withdrawsId}/reject" method:"put" tags:"AdminSettlements" summary:"拒绝结算申请"`
	WithdrawsId uint   `json:"withdrawsId" v:"required#结算记录ID不能为空" dc:"结算记录ID"`
	Reason      string `json:"reason" v:"required|length:1,200" dc:"拒绝原因"`
}

// RejectSettlementRes defines the response structure after rejecting a settlement.
type RejectSettlementRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
	// Message provides additional information about the rejection.
	Message string `json:"message" dc:"操作结果信息"`
	// TransactionIds contains the IDs of the generated transaction records for unfreezing.
	TransactionIds []uint64 `json:"transactionIds" dc:"解冻交易记录ID"`
}
